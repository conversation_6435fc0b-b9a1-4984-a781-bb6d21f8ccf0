import type { IntrinsicElements } from '../../intrinsic-elements';
import type { FC, PropsWithChildren, RefObject } from '../../types';
export declare const clearCache: () => void;
export declare const composeRef: <T>(ref: RefObject<T> | Function | undefined, cb: (e: T) => void | (() => void)) => ((e: T) => () => void);
export declare const title: FC<PropsWithChildren>;
export declare const script: FC<PropsWithChildren<IntrinsicElements["script"]>>;
export declare const style: FC<PropsWithChildren<IntrinsicElements["style"]>>;
export declare const link: FC<PropsWithChildren<IntrinsicElements["link"]>>;
export declare const meta: FC<PropsWithChildren>;
export declare const form: FC<PropsWithChildren<{
    action?: Function | string;
    method?: "get" | "post";
    ref?: RefObject<HTMLFormElement> | ((e: HTMLFormElement | null) => void | (() => void));
}>>;
export declare const input: FC<PropsWithChildren<IntrinsicElements["input"]>>;
export declare const button: FC<PropsWithChildren<IntrinsicElements["button"]>>;
