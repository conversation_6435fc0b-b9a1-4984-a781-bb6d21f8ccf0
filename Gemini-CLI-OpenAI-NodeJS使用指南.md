# Gemini CLI OpenAI - Node.js 本地运行使用指南

## 🚀 项目概述

本指南详细介绍如何在Debian/Linux系统上使用Node.js直接运行Gemini CLI OpenAI项目，无需Docker。该项目将Google Gemini模型转换为OpenAI兼容的API接口。

## 📋 系统要求

- **操作系统**: Debian/Ubuntu/Linux
- **Node.js**: v20.0.0 或更高版本（推荐 v22+）
- **npm**: v8.0.0 或更高版本
- **网络**: 需要访问Google API和GitHub

## 🛠️ 安装步骤

### 1. 检查系统环境

```bash
# 检查Node.js版本
node --version  # 应该显示 v20+ 或更高

# 检查npm版本
npm --version   # 应该显示 v8+ 或更高
```

### 2. 获取OAuth2凭据

#### 方法一：使用Gemini CLI（推荐）

```bash
# 安装Gemini CLI
npm install -g @google/gemini-cli

# 启动认证流程
gemini

# 选择 "● Login with Google"
# 在浏览器中完成Google账户登录
```

#### 方法二：手动查找凭据文件

凭据文件位置：
- **Linux/macOS**: `~/.gemini/oauth_creds.json`
- **Windows**: `C:\Users\<USER>\.gemini\oauth_creds.json`

### 3. 克隆项目

```bash
# 克隆项目到本地
git clone https://github.com/GewoonJaap/gemini-cli-openai.git
cd gemini-cli-openai
```

### 4. 安装依赖

```bash
# 安装项目依赖
npm install

# 全局安装wrangler CLI
npm install -g wrangler
```

### 5. 配置环境变量

```bash
# 复制环境变量模板
cp .dev.vars.example .dev.vars

# 编辑环境变量文件
nano .dev.vars  # 或使用其他编辑器
```

#### 环境变量配置示例

```bash
# 必需：OAuth2凭据（从 ~/.gemini/oauth_creds.json 复制）
GCP_SERVICE_ACCOUNT={"access_token":"ya29.a0AS3H6Nx...","refresh_token":"1//09FtpJYpxOd...","scope":"https://www.googleapis.com/auth/cloud-platform ...","token_type":"Bearer","id_token":"eyJhbGciOiJSUzI1NiIs...","expiry_date":*************}

# 可选：API密钥（用于认证，如不设置则API为公开访问）
OPENAI_API_KEY=sk-your-secret-api-key-here

# 可选：启用虚假思维模式（用于测试）
ENABLE_FAKE_THINKING=true

# 可选：启用真实思维模式
ENABLE_REAL_THINKING=true

# 可选：以内容形式流式传输思维
STREAM_THINKING_AS_CONTENT=true

# 可选：启用自动模型切换
ENABLE_AUTO_MODEL_SWITCHING=true
```

### 6. 启动服务器

```bash
# 启动开发服务器
npm run dev

# 服务器将在以下地址启动：
# - http://127.0.0.1:8787
# - http://0.0.0.0:8787
```

## 🧪 API测试

### 1. 健康检查

```bash
curl http://127.0.0.1:8787/health
```

**预期响应**:
```json
{"status":"ok","timestamp":"2025-01-30T12:00:00.000Z"}
```

### 2. 获取模型列表

```bash
curl -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/models
```

**预期响应**:
```json
{
  "object": "list",
  "data": [
    {
      "id": "gemini-2.5-pro",
      "object": "model",
      "created": **********,
      "owned_by": "google-gemini-cli"
    },
    {
      "id": "gemini-2.5-flash",
      "object": "model",
      "created": **********,
      "owned_by": "google-gemini-cli"
    }
  ]
}
```

### 3. 基本聊天测试

```bash
curl -X POST http://127.0.0.1:8787/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-secret-api-key-here" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "你好，请用中文回复"
      }
    ]
  }'
```

## 💻 Node.js代码示例

### 1. 基本聊天示例

```javascript
// chat-example.js
const fetch = require('node-fetch'); // npm install node-fetch

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

async function chatWithGemini(message, model = 'gemini-2.5-flash') {
  try {
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'user', content: message }
        ],
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('聊天错误:', error);
    return null;
  }
}

// 使用示例
async function main() {
  const response = await chatWithGemini('请用中文介绍一下人工智能');
  console.log('Gemini回复:', response);
}

main();
```

### 2. 流式响应示例

```javascript
// stream-example.js
const fetch = require('node-fetch');

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

async function streamChatWithGemini(message) {
  try {
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'gemini-2.5-flash',
        messages: [
          { role: 'user', content: message }
        ],
        stream: true
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    console.log('Gemini回复:');
    console.log('─'.repeat(50));

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ') && !line.includes('[DONE]')) {
          try {
            const data = JSON.parse(line.slice(6));
            const content = data.choices?.[0]?.delta?.content;
            if (content) {
              process.stdout.write(content);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }

    console.log('\n' + '─'.repeat(50));
  } catch (error) {
    console.error('流式聊天错误:', error);
  }
}

// 使用示例
streamChatWithGemini('请详细解释什么是机器学习，并给出一些实际应用例子');
```

### 3. 图像分析示例

```javascript
// image-example.js
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

function encodeImageToBase64(imagePath) {
  const imageBuffer = fs.readFileSync(imagePath);
  const base64Image = imageBuffer.toString('base64');
  const ext = path.extname(imagePath).toLowerCase();
  
  let mimeType = 'image/jpeg';
  if (ext === '.png') mimeType = 'image/png';
  else if (ext === '.gif') mimeType = 'image/gif';
  else if (ext === '.webp') mimeType = 'image/webp';
  
  return `data:${mimeType};base64,${base64Image}`;
}

async function analyzeImage(imagePath, question = '请描述这张图片') {
  try {
    const imageDataUrl = encodeImageToBase64(imagePath);
    
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'gemini-2.5-flash',
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: question },
              { 
                type: 'image_url', 
                image_url: { url: imageDataUrl } 
              }
            ]
          }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('图像分析错误:', error);
    return null;
  }
}

// 使用示例
async function main() {
  const imagePath = './example.jpg'; // 替换为您的图片路径
  if (fs.existsSync(imagePath)) {
    const analysis = await analyzeImage(imagePath, '请详细描述这张图片的内容');
    console.log('图像分析结果:', analysis);
  } else {
    console.log('图片文件不存在:', imagePath);
  }
}

main();
```

### 4. 工具调用示例

```javascript
// function-calling-example.js
const fetch = require('node-fetch');

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

async function chatWithTools(message) {
  try {
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'user', content: message }
        ],
        tools: [
          {
            type: 'function',
            function: {
              name: 'get_weather',
              description: '获取指定城市的天气信息',
              parameters: {
                type: 'object',
                properties: {
                  city: {
                    type: 'string',
                    description: '城市名称'
                  },
                  unit: {
                    type: 'string',
                    enum: ['celsius', 'fahrenheit'],
                    description: '温度单位'
                  }
                },
                required: ['city']
              }
            }
          }
        ],
        tool_choice: 'auto'
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();
    const message = data.choices[0].message;

    if (message.tool_calls) {
      console.log('🔧 模型调用了工具:');
      message.tool_calls.forEach(call => {
        console.log(`- 函数: ${call.function.name}`);
        console.log(`- 参数: ${call.function.arguments}`);
      });
    }

    return message.content || '模型调用了工具函数';
  } catch (error) {
    console.error('工具调用错误:', error);
    return null;
  }
}

// 使用示例
async function main() {
  const response = await chatWithTools('北京今天的天气怎么样？');
  console.log('🤖 回复:', response);
}

main();
```

### 5. 思维模式示例

```javascript
// thinking-example.js
const fetch = require('node-fetch');

const API_BASE = 'http://127.0.0.1:8787/v1';
const API_KEY = 'sk-your-secret-api-key-here';

async function chatWithThinking(message) {
  try {
    const response = await fetch(`${API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'gemini-2.5-pro',
        messages: [
          { role: 'user', content: message }
        ],
        include_reasoning: true,
        thinking_budget: 2048,
        stream: true
      })
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    console.log('🧠 Gemini思维过程和回复:');
    console.log('─'.repeat(60));

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ') && !line.includes('[DONE]')) {
          try {
            const data = JSON.parse(line.slice(6));
            const delta = data.choices?.[0]?.delta;

            if (delta?.reasoning) {
              console.log('💭 [思维]:', delta.reasoning);
            }

            if (delta?.content) {
              process.stdout.write(delta.content);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }

    console.log('\n' + '─'.repeat(60));
  } catch (error) {
    console.error('思维模式错误:', error);
  }
}

// 使用示例
chatWithThinking('请逐步解决：如果一个圆的半径是5cm，面积和周长分别是多少？');
```

## 🔧 高级配置

### 1. 环境变量详解

| 变量名 | 必需 | 描述 | 示例值 |
|--------|------|------|--------|
| `GCP_SERVICE_ACCOUNT` | ✅ | Google OAuth2凭据JSON | `{"access_token":"..."}` |
| `OPENAI_API_KEY` | ❌ | API认证密钥 | `sk-your-key-here` |
| `GEMINI_PROJECT_ID` | ❌ | Google Cloud项目ID | `my-project-123` |
| `ENABLE_FAKE_THINKING` | ❌ | 启用虚假思维模式 | `true` |
| `ENABLE_REAL_THINKING` | ❌ | 启用真实思维模式 | `true` |
| `STREAM_THINKING_AS_CONTENT` | ❌ | 思维内容流式传输 | `true` |
| `ENABLE_AUTO_MODEL_SWITCHING` | ❌ | 自动模型切换 | `true` |

### 2. 自定义端口

修改 `wrangler.toml` 文件：

```toml
[dev]
ip = "0.0.0.0"
port = 3000  # 改为您想要的端口
```

### 3. 内容安全设置

```bash
# 在 .dev.vars 中添加
GEMINI_MODERATION_HARASSMENT_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_HATE_SPEECH_THRESHOLD=BLOCK_NONE
GEMINI_MODERATION_SEXUALLY_EXPLICIT_THRESHOLD=BLOCK_SOME
GEMINI_MODERATION_DANGEROUS_CONTENT_THRESHOLD=BLOCK_ONLY_HIGH
```

## 🚨 故障排除

### 1. 常见错误

**错误**: `Missing Authorization header`
**解决**: 确保在请求头中包含正确的API密钥

**错误**: `Token refresh failed`
**解决**: 重新运行 `gemini` 命令获取新的OAuth凭据

**错误**: `Project ID discovery failed`
**解决**: 在 `.dev.vars` 中手动设置 `GEMINI_PROJECT_ID`

### 2. 调试端点

```bash
# 检查token缓存状态
curl -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/debug/cache

# 测试认证
curl -X POST -H "Authorization: Bearer sk-your-secret-api-key-here" \
     http://127.0.0.1:8787/v1/token-test
```

## 📊 性能优化

### 1. 模型选择建议

- **gemini-2.5-flash**: 适合快速响应，日常对话
- **gemini-2.5-pro**: 适合复杂推理，深度分析

### 2. 批量请求示例

```javascript
async function batchRequests(messages) {
  const promises = messages.map(message =>
    chatWithGemini(message, 'gemini-2.5-flash')
  );

  const results = await Promise.all(promises);
  return results;
}
```

## 🔗 与OpenAI SDK兼容

```javascript
// 使用官方OpenAI SDK
const { Configuration, OpenAIApi } = require('openai');

const configuration = new Configuration({
  apiKey: 'sk-your-secret-api-key-here',
  basePath: 'http://127.0.0.1:8787/v1'
});

const openai = new OpenAIApi(configuration);

async function useOpenAISDK() {
  const completion = await openai.createChatCompletion({
    model: 'gemini-2.5-flash',
    messages: [{ role: 'user', content: '你好' }]
  });

  console.log(completion.data.choices[0].message.content);
}
```

## 📝 总结

✅ **成功在Debian系统上运行gemini-cli-openai项目**
- 使用Node.js直接运行（无需Docker）
- OAuth2认证正常工作
- API功能完全正常
- 支持OpenAI兼容的接口

**服务器地址**: `http://127.0.0.1:8787`
**API兼容性**: 完全兼容OpenAI API格式
**支持的模型**: `gemini-2.5-pro`, `gemini-2.5-flash`

现在您可以像使用OpenAI API一样使用Google的Gemini模型了！🎉
