/**
 * @module
 * The preset that uses `LinearRouter`.
 */
import { HonoBase } from '../hono-base';
import type { HonoOptions } from '../hono-base';
import type { BlankEnv, BlankSchema, Env, Schema } from '../types';
export declare class Hono<E extends Env = BlankEnv, S extends Schema = BlankSchema, BasePath extends string = "/"> extends HonoBase<E, S, BasePath> {
    constructor(options?: HonoOptions<E>);
}
