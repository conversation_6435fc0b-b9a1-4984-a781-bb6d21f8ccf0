{"clientTcpRtt": 1, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 31898, "clientAcceptEncoding": "gzip, deflate, br", "country": "SG", "isEUCountry": false, "verifiedBotCategory": "", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "03zUhxFslFdX97S8FRZu+eOibuKyVJZHZzQ7ez/GXNQ=", "tlsExportedAuthenticator": {"clientFinished": "3720ed7aeef5c8ced60ce15d27173334d15aa5fecf847b77d1de30589235716960c4e1e26d537ce04ee724a09543caea", "clientHandshake": "30eff1d8f16ed3fe3ba2319a39deac8a890313b7c7da8f9a21b194b063487967b6d89fa5bd7e725ba334fde7dbbbf62d", "serverHandshake": "aa1c686895e9f181d143870e9e559624c3273551c790b3165aac128c57a78b4328aec66b0e4efae3286b1ccd019ad32b", "serverFinished": "d2a82b252c445dfe6bd51f0be28bc2cfc61b4c94902e96d96d2029749fe665685ed123ef62485946585ae6ab84efa9c6"}, "tlsClientHelloLength": "386", "colo": "SIN", "timezone": "Asia/Singapore", "longitude": "103.95944", "latitude": "1.37778", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "506819", "city": "Kampong Loyang", "tlsVersion": "TLSv1.3", "asOrganization": "Oracle Corporation", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}