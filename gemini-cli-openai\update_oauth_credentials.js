#!/usr/bin/env node

/**
 * OAuth凭据更新工具
 * 用于更新Gemini CLI OpenAI项目的OAuth凭据
 * 
 * 使用方法:
 * node update_oauth_credentials.js [凭据文件路径]
 * 
 * 示例:
 * node update_oauth_credentials.js I:/gemini/oauth_creds.json
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class OAuthUpdater {
  constructor() {
    this.devVarsPath = './gemini-cli-openai/.dev.vars';
    this.backupDir = './oauth_backups';
  }

  // 创建备份目录
  ensureBackupDir() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      console.log(`📁 创建备份目录: ${this.backupDir}`);
    }
  }

  // 验证凭据文件格式
  validateCredentials(credsPath) {
    try {
      console.log(`🔍 验证凭据文件: ${credsPath}`);
      
      if (!fs.existsSync(credsPath)) {
        throw new Error(`凭据文件不存在: ${credsPath}`);
      }

      const credsContent = fs.readFileSync(credsPath, 'utf8');
      const creds = JSON.parse(credsContent);
      
      const requiredFields = [
        'access_token', 'refresh_token', 'scope', 
        'token_type', 'id_token', 'expiry_date'
      ];
      
      const missingFields = [];
      for (const field of requiredFields) {
        if (!creds[field]) {
          missingFields.push(field);
        }
      }
      
      if (missingFields.length > 0) {
        throw new Error(`缺少必需字段: ${missingFields.join(', ')}`);
      }

      // 检查token过期时间
      const now = Date.now();
      const expiryDate = creds.expiry_date;
      
      if (expiryDate < now) {
        const expiredTime = new Date(expiryDate).toLocaleString();
        console.log(`⚠️  警告: Token已过期 (过期时间: ${expiredTime})`);
        console.log('   建议重新运行 gemini 命令获取新token');
      } else {
        const timeLeft = Math.floor((expiryDate - now) / 1000 / 60);
        console.log(`✅ Token有效 (剩余时间: ${timeLeft}分钟)`);
      }

      console.log('✅ 凭据格式验证通过');
      return creds;
      
    } catch (error) {
      console.error('❌ 凭据验证失败:', error.message);
      return null;
    }
  }

  // 备份当前配置
  backupCurrentConfig() {
    try {
      if (!fs.existsSync(this.devVarsPath)) {
        console.log('⚠️  .dev.vars文件不存在，跳过备份');
        return null;
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(this.backupDir, `.dev.vars.backup.${timestamp}`);
      
      fs.copyFileSync(this.devVarsPath, backupPath);
      console.log(`💾 配置已备份到: ${backupPath}`);
      return backupPath;
      
    } catch (error) {
      console.error('❌ 备份失败:', error.message);
      return null;
    }
  }

  // 更新环境变量文件
  updateDevVars(credentials) {
    try {
      console.log('🔄 更新环境变量文件...');
      
      // 将凭据转换为单行JSON
      const credsJson = JSON.stringify(credentials);
      
      if (!fs.existsSync(this.devVarsPath)) {
        // 如果.dev.vars不存在，从示例文件复制
        const examplePath = './gemini-cli-openai/.dev.vars.example';
        if (fs.existsSync(examplePath)) {
          fs.copyFileSync(examplePath, this.devVarsPath);
          console.log('📋 从示例文件创建.dev.vars');
        } else {
          throw new Error('.dev.vars和.dev.vars.example都不存在');
        }
      }

      // 读取当前配置
      let content = fs.readFileSync(this.devVarsPath, 'utf8');
      
      // 替换GCP_SERVICE_ACCOUNT行
      const gcpServiceAccountRegex = /^GCP_SERVICE_ACCOUNT=.*$/m;
      const newLine = `GCP_SERVICE_ACCOUNT=${credsJson}`;
      
      if (gcpServiceAccountRegex.test(content)) {
        content = content.replace(gcpServiceAccountRegex, newLine);
      } else {
        // 如果没找到GCP_SERVICE_ACCOUNT行，添加到文件开头
        content = `${newLine}\n\n${content}`;
      }
      
      // 写入更新后的内容
      fs.writeFileSync(this.devVarsPath, content);
      console.log('✅ 环境变量已更新');
      
      return true;
      
    } catch (error) {
      console.error('❌ 更新环境变量失败:', error.message);
      return false;
    }
  }

  // 测试新凭据
  async testCredentials() {
    console.log('🧪 测试新凭据...');
    
    try {
      // 检查服务器是否运行
      const fetch = require('node-fetch');
      const response = await fetch('http://127.0.0.1:8787/health', {
        timeout: 5000
      });
      
      if (response.ok) {
        console.log('✅ 服务器响应正常');
        
        // 测试API功能
        const modelsResponse = await fetch('http://127.0.0.1:8787/v1/models', {
          headers: {
            'Authorization': 'Bearer sk-iU&F^QY4YjTMe^UrfEnwkvYf@8RcW7zU'
          },
          timeout: 10000
        });
        
        if (modelsResponse.ok) {
          const models = await modelsResponse.json();
          console.log('✅ API功能正常');
          console.log(`📋 可用模型: ${models.data.map(m => m.id).join(', ')}`);
        } else {
          console.log('⚠️  API测试失败，可能需要重启服务器');
        }
      } else {
        console.log('⚠️  服务器未运行，请启动服务器测试新凭据');
      }
      
    } catch (error) {
      console.log('⚠️  无法连接到服务器，请手动启动服务器测试');
      console.log('   运行: cd gemini-cli-openai && npm run dev');
    }
  }

  // 主要更新流程
  async update(credsPath) {
    console.log('🚀 开始OAuth凭据更新流程...\n');
    
    // 1. 确保备份目录存在
    this.ensureBackupDir();
    
    // 2. 验证新凭据
    const credentials = this.validateCredentials(credsPath);
    if (!credentials) {
      return false;
    }
    
    // 3. 备份当前配置
    this.backupCurrentConfig();
    
    // 4. 更新环境变量
    const updateSuccess = this.updateDevVars(credentials);
    if (!updateSuccess) {
      return false;
    }
    
    // 5. 测试新凭据
    await this.testCredentials();
    
    console.log('\n🎉 OAuth凭据更新完成！');
    console.log('📝 后续步骤:');
    console.log('   1. 重启服务器: cd gemini-cli-openai && npm run dev');
    console.log('   2. 测试API功能');
    console.log('   3. 如有问题，可从备份恢复配置');
    
    return true;
  }

  // 显示帮助信息
  showHelp() {
    console.log(`
📖 OAuth凭据更新工具使用说明

用法:
  node update_oauth_credentials.js [凭据文件路径]

参数:
  凭据文件路径    OAuth凭据JSON文件的路径 (可选)
                 默认: I:/gemini/oauth_creds.json

示例:
  node update_oauth_credentials.js
  node update_oauth_credentials.js I:/gemini/oauth_creds.json
  node update_oauth_credentials.js ~/.gemini/oauth_creds.json

功能:
  ✅ 验证OAuth凭据格式
  ✅ 自动备份当前配置
  ✅ 更新环境变量文件
  ✅ 测试新凭据功能
  ✅ 提供详细的操作反馈

注意事项:
  - 确保凭据文件包含所有必需字段
  - 运行前请确保在正确的项目目录中
  - 更新后需要重启服务器以应用新凭据
`);
  }
}

// 主程序
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    new OAuthUpdater().showHelp();
    return;
  }
  
  const credsPath = args[0] || 'I:/gemini/oauth_creds.json';
  const updater = new OAuthUpdater();
  
  try {
    const success = await updater.update(credsPath);
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 更新过程中发生错误:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = OAuthUpdater;
